{"version": 3, "sources": ["src/pages/404.tsx"], "sourcesContent": ["import { history } from '@umijs/max';\r\nimport { <PERSON><PERSON>, Card, Result } from 'antd';\r\nimport React from 'react';\r\n\r\nconst NoFoundPage: React.FC = () => (\r\n  <Card variant=\"borderless\">\r\n    <Result\r\n      status=\"404\"\r\n      title=\"404\"\r\n      subTitle={useIntl().formatMessage({ id: 'pages.404.subTitle' })}\r\n      extra={\r\n        <Button type=\"primary\" onClick={() => history.push('/')}>\r\n          {useIntl().formatMessage({ id: 'pages.404.buttonText' })}\r\n        </Button>\r\n      }\r\n    />\r\n  </Card>\r\n);\r\n\r\nexport default NoFoundPage;\r\n"], "names": [], "mappings": ";;;;;;;4BAmBA;;;eAAA;;;;;;;4BAnBwB;6BACa;uEACnB;;;;;;;;;;AAElB,MAAM,cAAwB;;IAAM,qBAClC,2BAAC,UAAI;QAAC,SAAQ;kBACZ,cAAA,2BAAC,YAAM;YACL,QAAO;YACP,OAAM;YACN,UAAU,UAAU,aAAa,CAAC;gBAAE,IAAI;YAAqB;YAC7D,qBACE,2BAAC,YAAM;gBAAC,MAAK;gBAAU,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0BAChD,UAAU,aAAa,CAAC;oBAAE,IAAI;gBAAuB;;;;;;;;;;;;;;;;AAKhE;GAbM;KAAA;IAeN,WAAe"}